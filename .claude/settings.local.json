{"permissions": {"allow": ["WebFetch(domain:mcpservers.org)", "Bash(grep:*)", "Bash(npm run:*)", "Bash(npx tsc:*)", "mcp__filesystem__directory_tree", "mcp__filesystem__list_directory", "Bash(bun add:*)", "Bash(npm install:*)", "Bash(cargo check:*)", "Bash(find:*)", "Bash(cargo build:*)", "Bash(bunx tsc:*)", "Bash(npx vite:*)", "Bash(rg:*)", "mcp__serena__initial_instructions", "mcp__serena__check_onboarding_performed", "mcp__serena__onboarding", "mcp__serena__get_symbols_overview", "mcp__serena__list_dir", "mcp__serena__search_for_pattern", "mcp__serena__write_memory", "mcp__serena__replace_regex", "mcp__serena__find_symbol", "mcp__serena__read_memory", "Bash(ls:*)", "mcp__sequential-thinking__sequentialthinking", "Bash(rm:*)", "Bash(cargo test:*)", "Bash(uvx:*)", "<PERSON><PERSON>(claude-code config)", "Bash(cc config)", "mcp__serena__list_memories", "mcp__serena__think_about_collected_information", "<PERSON><PERSON>(curl:*)", "Bash(timeout 10 npm run dev:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(chmod:*)", "Bash(pgrep:*)", "Bash(bun run:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(touch:*)"], "deny": []}}