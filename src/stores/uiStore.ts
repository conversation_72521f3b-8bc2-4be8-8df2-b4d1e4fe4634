import { create } from 'zustand';

type View = "welcome" | "projects" | "agents" | "editor" | "settings" | "claude-file-editor" | "claude-code-session" | "usage-dashboard" | "mcp" | "templates" | "performance" | "plugins" | "agent-system";

interface UIState {
  view: View;
  loading: boolean;
  error: string | null;
  showNFO: boolean;
  showClaudeBinaryDialog: boolean;
  isClaudeStreaming: boolean;
  activeClaudeSessionId: string | null;
  setView: (view: View) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setShowNFO: (show: boolean) => void;
  setShowClaudeBinaryDialog: (show: boolean) => void;
  setClaudeStreaming: (isStreaming: boolean, sessionId: string | null) => void;
}

export const useUIStore = create<UIState>((set, get) => ({
  view: "welcome",
  loading: true,
  error: null,
  showNFO: false,
  showClaudeBinaryDialog: false,
  isClaudeStreaming: false,
  activeClaudeSessionId: null,
  setView: (view) => {
    const { isClaudeStreaming, activeClaudeSessionId } = get();
    if (view !== 'claude-code-session' && isClaudeStreaming && activeClaudeSessionId) {
        const shouldLeave = window.confirm(
          "Claude is still responding. If you navigate away, Claude will continue running in the background.\n\n" +
          "You can return to this session from the Projects view.\n\n" +
          "Do you want to continue?"
        );
        if (!shouldLeave) {
          return;
        }
      }
    set({ view });
  },
  setLoading: (loading) => set({ loading }),
  setError: (error) => set({ error }),
  setShowNFO: (show) => set({ showNFO: show }),
  setShowClaudeBinaryDialog: (show) => set({ showClaudeBinaryDialog: show }),
  setClaudeStreaming: (isStreaming, sessionId) => set({ isClaudeStreaming: isStreaming, activeClaudeSessionId: sessionId }),
}));