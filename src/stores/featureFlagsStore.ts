import { create } from 'zustand';
import { defaultFeatureFlags, type FeatureFlags } from '@/lib/featureFlags';

interface FeatureFlagsState {
  flags: FeatureFlags;
  updateFlags: (updates: Partial<FeatureFlags>) => void;
  isEnabled: (feature: keyof FeatureFlags) => boolean;
  resetToDefaults: () => void;
}

// Deep merge helper function
function deepMerge<T extends Record<string, any>>(target: T, source: Partial<T>): T {
  const result = { ...target };
  
  for (const key in source) {
    if (source[key] !== undefined) {
      if (typeof source[key] === 'object' && !Array.isArray(source[key]) && source[key] !== null) {
        result[key] = deepMerge(
          result[key] as Record<string, any>,
          source[key] as Record<string, any>
        ) as T[typeof key];
      } else {
        result[key] = source[key] as T[typeof key];
      }
    }
  }
  
  return result;
}

// Get initial feature flags from localStorage or use defaults
function getInitialFlags(): FeatureFlags {
  if (typeof window === 'undefined') {
    return defaultFeatureFlags;
  }

  const stored = localStorage.getItem('claudia-feature-flags');
  if (stored) {
    try {
      const parsed = JSON.parse(stored);
      return deepMerge(defaultFeatureFlags, parsed);
    } catch (error) {
      // Failed to parse stored feature flags, reset to defaults
      localStorage.removeItem('claudia-feature-flags');
    }
  }

  return defaultFeatureFlags;
}

export const useFeatureFlagsStore = create<FeatureFlagsState>((set, get) => ({
  flags: getInitialFlags(),

  updateFlags: (updates: Partial<FeatureFlags>) => {
    const current = get().flags;
    const updated = deepMerge(current, updates);
    
    set({ flags: updated });
    
    // Persist to localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('claudia-feature-flags', JSON.stringify(updated));
    }
  },

  isEnabled: (feature: keyof FeatureFlags) => {
    const flags = get().flags;
    return flags[feature]?.enabled ?? false;
  },

  resetToDefaults: () => {
    set({ flags: defaultFeatureFlags });
    
    // Clear localStorage
    if (typeof window !== 'undefined') {
      localStorage.removeItem('claudia-feature-flags');
    }
  },
}));