import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Lightbulb,
  X,
  Plus,
  Sparkles,
  TrendingUp,
  Clock,
  Users,
  Star,
  Download,
  CheckCircle,
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { api, type MCPServer } from '@/lib/api';
import type { ClaudeStreamMessage } from './AgentExecution';

interface MCPRecommendationEngineProps {
  messages: ClaudeStreamMessage[];
  projectPath: string;
  onInstallMCP: (serverName: string) => Promise<void>;
  onDismiss: () => void;
  className?: string;
}

interface MCPRecommendation {
  name: string;
  description: string;
  reason: string;
  confidence: number;
  category: 'development' | 'data' | 'ai' | 'cloud' | 'security';
  estimatedUsage: 'high' | 'medium' | 'low';
  popularity: number;
  installation: {
    command: string;
    args: string[];
  };
}

export const MCPRecommendationEngine: React.FC<MCPRecommendationEngineProps> = ({
  messages,
  projectPath,
  onInstallMCP,
  onDismiss,
  className,
}) => {
  const [recommendations, setRecommendations] = useState<MCPRecommendation[]>([]);
  const [installingServers, setInstallingServers] = useState<Set<string>>(new Set());
  const [installedServers, setInstalledServers] = useState<Set<string>>(new Set());
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  useEffect(() => {
    analyzeMessagesForRecommendations();
  }, [messages, projectPath]);

  const analyzeMessagesForRecommendations = async () => {
    if (messages.length < 3) return; // Need some conversation context
    
    setIsAnalyzing(true);
    
    try {
      // Analyze conversation context
      const conversationText = messages
        .filter(m => m.type === 'user' || m.type === 'assistant')
        .map(m => {
          if (m.message?.content) {
            return Array.isArray(m.message.content) 
              ? m.message.content.map(c => c.type === 'text' ? c.text : '').join(' ')
              : m.message.content;
          }
          return '';
        })
        .join(' ')
        .toLowerCase();

      const recommendations = generateRecommendations(conversationText, projectPath);
      setRecommendations(recommendations);
    } catch (error) {
      console.error('Failed to analyze messages for MCP recommendations:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const generateRecommendations = (conversationText: string, projectPath: string): MCPRecommendation[] => {
    const recommendations: MCPRecommendation[] = [];
    
    // Development-related recommendations
    if (conversationText.includes('git') || conversationText.includes('repository') || conversationText.includes('commit')) {
      recommendations.push({
        name: 'git',
        description: 'Git version control operations',
        reason: 'Detected Git-related discussions in your conversation',
        confidence: 85,
        category: 'development',
        estimatedUsage: 'high',
        popularity: 95,
        installation: { command: 'npx', args: ['-y', '@modelcontextprotocol/server-git'] }
      });
    }

    if (conversationText.includes('github') || conversationText.includes('pull request') || conversationText.includes('issue')) {
      recommendations.push({
        name: 'github',
        description: 'GitHub repository integration',
        reason: 'GitHub operations mentioned in conversation',
        confidence: 90,
        category: 'development',
        estimatedUsage: 'high',
        popularity: 88,
        installation: { command: 'npx', args: ['-y', '@modelcontextprotocol/server-github'] }
      });
    }

    // Data-related recommendations
    if (conversationText.includes('database') || conversationText.includes('sql') || conversationText.includes('query')) {
      recommendations.push({
        name: 'sqlite',
        description: 'Local SQLite database operations',
        reason: 'Database operations discussed in conversation',
        confidence: 80,
        category: 'data',
        estimatedUsage: 'medium',
        popularity: 75,
        installation: { command: 'npx', args: ['-y', '@modelcontextprotocol/server-sqlite'] }
      });
    }

    if (conversationText.includes('search') || conversationText.includes('web') || conversationText.includes('internet')) {
      recommendations.push({
        name: 'brave-search',
        description: 'Web search capabilities',
        reason: 'Web search functionality would enhance your workflow',
        confidence: 70,
        category: 'data',
        estimatedUsage: 'medium',
        popularity: 82,
        installation: { command: 'npx', args: ['-y', '@modelcontextprotocol/server-brave-search'] }
      });
    }

    // AI/ML recommendations
    if (conversationText.includes('memory') || conversationText.includes('remember') || conversationText.includes('context')) {
      recommendations.push({
        name: 'memory',
        description: 'Persistent memory across sessions',
        reason: 'Memory and context management would be beneficial',
        confidence: 75,
        category: 'ai',
        estimatedUsage: 'high',
        popularity: 90,
        installation: { command: 'npx', args: ['-y', '@modelcontextprotocol/server-memory'] }
      });
    }

    // Cloud/Infrastructure recommendations
    if (conversationText.includes('docker') || conversationText.includes('container')) {
      recommendations.push({
        name: 'docker',
        description: 'Docker container management',
        reason: 'Docker operations detected in conversation',
        confidence: 85,
        category: 'cloud',
        estimatedUsage: 'medium',
        popularity: 78,
        installation: { command: 'npx', args: ['-y', '@modelcontextprotocol/server-docker'] }
      });
    }

    if (conversationText.includes('aws') || conversationText.includes('amazon') || conversationText.includes('cloud')) {
      recommendations.push({
        name: 'aws',
        description: 'AWS cloud services integration',
        reason: 'AWS or cloud services mentioned',
        confidence: 70,
        category: 'cloud',
        estimatedUsage: 'low',
        popularity: 65,
        installation: { command: 'npx', args: ['-y', '@modelcontextprotocol/server-aws'] }
      });
    }

    // Project-specific recommendations based on file structure
    if (projectPath.includes('node_modules') || projectPath.includes('package.json')) {
      if (!recommendations.find(r => r.name === 'github')) {
        recommendations.push({
          name: 'github',
          description: 'GitHub repository integration',
          reason: 'Node.js project detected - GitHub integration recommended',
          confidence: 60,
          category: 'development',
          estimatedUsage: 'medium',
          popularity: 88,
          installation: { command: 'npx', args: ['-y', '@modelcontextprotocol/server-github'] }
        });
      }
    }

    // Sort by confidence and limit to top 4
    return recommendations
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, 4);
  };

  const handleInstallMCP = async (recommendation: MCPRecommendation) => {
    setInstallingServers(prev => new Set([...prev, recommendation.name]));
    
    try {
      await onInstallMCP(recommendation.name);
      setInstalledServers(prev => new Set([...prev, recommendation.name]));
    } catch (error) {
      console.error('Failed to install MCP server:', error);
    } finally {
      setInstallingServers(prev => {
        const newSet = new Set(prev);
        newSet.delete(recommendation.name);
        return newSet;
      });
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'development': return '🔧';
      case 'data': return '📊';
      case 'ai': return '🤖';
      case 'cloud': return '☁️';
      case 'security': return '🔒';
      default: return '⚡';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'development': return 'bg-blue-500';
      case 'data': return 'bg-green-500';
      case 'ai': return 'bg-purple-500';
      case 'cloud': return 'bg-cyan-500';
      case 'security': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  if (recommendations.length === 0 && !isAnalyzing) {
    return null;
  }

  return (
    <TooltipProvider>
      <motion.div
        initial={{ opacity: 0, y: 20, scale: 0.95 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        exit={{ opacity: 0, y: 20, scale: 0.95 }}
        className={`fixed bottom-32 right-6 w-80 z-40 ${className}`}
      >
        <Card className="bg-background/95 backdrop-blur-md border shadow-xl">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2 text-sm">
                <Lightbulb className="h-4 w-4 text-yellow-500" />
                MCP Recommendations
                {isAnalyzing && <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse" />}
              </CardTitle>
              <Button
                variant="ghost"
                size="icon"
                onClick={onDismiss}
                className="h-6 w-6"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
            <p className="text-xs text-muted-foreground">
              Based on your conversation and project
            </p>
          </CardHeader>
          
          <CardContent className="space-y-3">
            <AnimatePresence>
              {recommendations.map((rec, index) => {
                const isInstalling = installingServers.has(rec.name);
                const isInstalled = installedServers.has(rec.name);
                
                return (
                  <motion.div
                    key={rec.name}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ delay: index * 0.1 }}
                    className="border rounded-lg p-3 space-y-2"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="text-sm font-medium">{rec.name}</span>
                          <Badge variant="secondary" className="text-xs h-4">
                            {getCategoryIcon(rec.category)} {rec.category}
                          </Badge>
                          <div className="flex items-center gap-1">
                            <Star className="h-3 w-3 text-yellow-500" />
                            <span className="text-xs text-muted-foreground">{rec.popularity}%</span>
                          </div>
                        </div>
                        <p className="text-xs text-muted-foreground mb-1">{rec.description}</p>
                        <p className="text-xs text-blue-600">{rec.reason}</p>
                        
                        <div className="flex items-center gap-2 mt-2">
                          <div className="flex-1">
                            <div className="flex items-center justify-between text-xs mb-1">
                              <span>Confidence</span>
                              <span>{rec.confidence}%</span>
                            </div>
                            <Progress value={rec.confidence} className="h-1" />
                          </div>
                          <Badge 
                            variant={rec.estimatedUsage === 'high' ? 'default' : 'outline'} 
                            className="text-xs"
                          >
                            {rec.estimatedUsage} usage
                          </Badge>
                        </div>
                      </div>
                      
                      <div className="ml-2">
                        {isInstalled ? (
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <div className="flex items-center justify-center w-8 h-8 rounded-full bg-green-100 text-green-600">
                                <CheckCircle className="h-4 w-4" />
                              </div>
                            </TooltipTrigger>
                            <TooltipContent>Installed</TooltipContent>
                          </Tooltip>
                        ) : (
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleInstallMCP(rec)}
                                disabled={isInstalling}
                                className="h-8 w-8 p-0"
                              >
                                {isInstalling ? (
                                  <div className="w-3 h-3 border-2 border-primary border-t-transparent rounded-full animate-spin" />
                                ) : (
                                  <Plus className="h-3 w-3" />
                                )}
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>Install MCP Server</TooltipContent>
                          </Tooltip>
                        )}
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </AnimatePresence>
            
            {recommendations.length > 0 && (
              <div className="pt-2 border-t">
                <p className="text-xs text-muted-foreground text-center">
                  Recommendations update based on your conversation
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </motion.div>
    </TooltipProvider>
  );
};