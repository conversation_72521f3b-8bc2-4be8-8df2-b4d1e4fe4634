import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Play, 
  Pause, 
  Square, 
  Plus, 
  Trash2, 
  Edit, 
  GitBranch,
  Code,
  TestTube,
  Rocket,
  CheckCircle,
  XCircle,
  Clock,
  Zap,
  Settings,
  ArrowRight,
  ArrowDown
} from 'lucide-react';

interface WorkflowStep {
  id: string;
  name: string;
  type: 'code' | 'test' | 'build' | 'deploy' | 'review' | 'custom';
  command: string;
  condition?: string;
  timeout: number;
  retries: number;
  enabled: boolean;
}

interface Workflow {
  id: string;
  name: string;
  description: string;
  trigger: 'manual' | 'file_change' | 'git_commit' | 'schedule';
  triggerConfig: any;
  steps: WorkflowStep[];
  enabled: boolean;
  lastRun?: Date;
  status: 'idle' | 'running' | 'success' | 'failed';
}

interface WorkflowExecution {
  id: string;
  workflowId: string;
  startTime: Date;
  endTime?: Date;
  status: 'running' | 'success' | 'failed';
  currentStep: number;
  stepResults: { stepId: string; status: 'pending' | 'running' | 'success' | 'failed'; output?: string }[];
}

const WORKFLOW_TEMPLATES = [
  {
    name: 'Full Development Pipeline',
    description: 'Complete CI/CD pipeline with testing and deployment',
    steps: [
      { name: 'Install Dependencies', type: 'code', command: 'npm install' },
      { name: 'Run Linting', type: 'code', command: 'npm run lint' },
      { name: 'Run Tests', type: 'test', command: 'npm test' },
      { name: 'Build Project', type: 'build', command: 'npm run build' },
      { name: 'Deploy to Staging', type: 'deploy', command: 'npm run deploy:staging' }
    ]
  },
  {
    name: 'Quick Code Check',
    description: 'Fast validation for code changes',
    steps: [
      { name: 'Type Check', type: 'code', command: 'tsc --noEmit' },
      { name: 'Format Check', type: 'code', command: 'prettier --check .' },
      { name: 'Unit Tests', type: 'test', command: 'npm run test:unit' }
    ]
  },
  {
    name: 'Release Preparation',
    description: 'Prepare and validate a release',
    steps: [
      { name: 'Version Bump', type: 'code', command: 'npm version patch' },
      { name: 'Generate Changelog', type: 'code', command: 'npm run changelog' },
      { name: 'Build Release', type: 'build', command: 'npm run build:prod' },
      { name: 'Run E2E Tests', type: 'test', command: 'npm run test:e2e' }
    ]
  }
];

export function WorkflowAutomation() {
  const [workflows, setWorkflows] = useState<Workflow[]>([]);
  const [executions, setExecutions] = useState<WorkflowExecution[]>([]);
  const [selectedWorkflow, setSelectedWorkflow] = useState<string | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [newWorkflow, setNewWorkflow] = useState<Partial<Workflow>>({
    name: '',
    description: '',
    trigger: 'manual',
    steps: [],
    enabled: true
  });

  // Initialize with sample workflows
  useEffect(() => {
    const sampleWorkflows: Workflow[] = [
      {
        id: '1',
        name: 'Development Pipeline',
        description: 'Full CI/CD pipeline for development',
        trigger: 'git_commit',
        triggerConfig: { branch: 'main' },
        enabled: true,
        status: 'idle',
        steps: [
          {
            id: 's1',
            name: 'Install Dependencies',
            type: 'code',
            command: 'npm install',
            timeout: 300,
            retries: 2,
            enabled: true
          },
          {
            id: 's2',
            name: 'Run Tests',
            type: 'test',
            command: 'npm test',
            timeout: 600,
            retries: 1,
            enabled: true
          },
          {
            id: 's3',
            name: 'Build Project',
            type: 'build',
            command: 'npm run build',
            timeout: 300,
            retries: 1,
            enabled: true
          }
        ]
      }
    ];
    setWorkflows(sampleWorkflows);
  }, []);

  const createWorkflowFromTemplate = (template: typeof WORKFLOW_TEMPLATES[0]) => {
    const workflow: Workflow = {
      id: Date.now().toString(),
      name: template.name,
      description: template.description,
      trigger: 'manual',
      triggerConfig: {},
      enabled: true,
      status: 'idle',
      steps: template.steps.map((step, index) => ({
        id: `step_${index}`,
        name: step.name,
        type: step.type as WorkflowStep['type'],
        command: step.command,
        timeout: 300,
        retries: 1,
        enabled: true
      }))
    };
    
    setWorkflows(prev => [...prev, workflow]);
  };

  const runWorkflow = async (workflowId: string) => {
    const workflow = workflows.find(w => w.id === workflowId);
    if (!workflow) return;

    // Update workflow status
    setWorkflows(prev => prev.map(w => 
      w.id === workflowId ? { ...w, status: 'running', lastRun: new Date() } : w
    ));

    // Create execution record
    const execution: WorkflowExecution = {
      id: Date.now().toString(),
      workflowId,
      startTime: new Date(),
      status: 'running',
      currentStep: 0,
      stepResults: workflow.steps.map(step => ({ stepId: step.id, status: 'pending' }))
    };

    setExecutions(prev => [execution, ...prev]);

    // Simulate workflow execution
    for (let i = 0; i < workflow.steps.length; i++) {
      const step = workflow.steps[i];
      if (!step.enabled) continue;

      // Update current step
      setExecutions(prev => prev.map(e => 
        e.id === execution.id 
          ? { 
              ...e, 
              currentStep: i,
              stepResults: e.stepResults.map((result, index) => 
                index === i ? { ...result, status: 'running' } : result
              )
            }
          : e
      ));

      // Simulate step execution
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Simulate random success/failure (90% success rate)
      const success = Math.random() > 0.1;
      
      setExecutions(prev => prev.map(e => 
        e.id === execution.id 
          ? { 
              ...e,
              stepResults: e.stepResults.map((result, index) => 
                index === i 
                  ? { 
                      ...result, 
                      status: success ? 'success' : 'failed',
                      output: success 
                        ? `✓ ${step.name} completed successfully`
                        : `✗ ${step.name} failed: Mock error for demo`
                    }
                  : result
              )
            }
          : e
      ));

      if (!success) {
        // Workflow failed
        setExecutions(prev => prev.map(e => 
          e.id === execution.id 
            ? { ...e, status: 'failed', endTime: new Date() }
            : e
        ));
        setWorkflows(prev => prev.map(w => 
          w.id === workflowId ? { ...w, status: 'failed' } : w
        ));
        return;
      }
    }

    // Workflow completed successfully
    setExecutions(prev => prev.map(e => 
      e.id === execution.id 
        ? { ...e, status: 'success', endTime: new Date() }
        : e
    ));
    setWorkflows(prev => prev.map(w => 
      w.id === workflowId ? { ...w, status: 'success' } : w
    ));
  };

  const getStepIcon = (type: WorkflowStep['type']) => {
    switch (type) {
      case 'code': return <Code className="w-4 h-4" />;
      case 'test': return <TestTube className="w-4 h-4" />;
      case 'build': return <Settings className="w-4 h-4" />;
      case 'deploy': return <Rocket className="w-4 h-4" />;
      case 'review': return <GitBranch className="w-4 h-4" />;
      default: return <Zap className="w-4 h-4" />;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed': return <XCircle className="w-4 h-4 text-red-500" />;
      case 'running': return (
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        >
          <Clock className="w-4 h-4 text-blue-500" />
        </motion.div>
      );
      default: return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <GitBranch className="w-5 h-5" />
            Workflow Automation
            <Badge variant="secondary">Pipeline Builder</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Quick Templates */}
          <div>
            <h3 className="font-medium mb-3">Quick Start Templates</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              {WORKFLOW_TEMPLATES.map((template, index) => (
                <Card key={index} className="cursor-pointer hover:bg-muted/50 transition-colors">
                  <CardContent className="pt-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-sm">{template.name}</h4>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => createWorkflowFromTemplate(template)}
                      >
                        <Plus className="w-3 h-3" />
                      </Button>
                    </div>
                    <p className="text-xs text-muted-foreground mb-3">{template.description}</p>
                    <div className="text-xs text-muted-foreground">
                      {template.steps.length} steps
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Workflows List */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-medium">Your Workflows</h3>
              <Button size="sm" onClick={() => setIsCreating(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Create Workflow
              </Button>
            </div>

            <div className="space-y-3">
              {workflows.map((workflow) => (
                <Card key={workflow.id}>
                  <CardContent className="pt-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <div>
                          <h4 className="font-medium">{workflow.name}</h4>
                          <p className="text-sm text-muted-foreground">{workflow.description}</p>
                        </div>
                        <Badge variant={workflow.enabled ? "default" : "secondary"}>
                          {workflow.trigger}
                        </Badge>
                        {getStatusIcon(workflow.status)}
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => runWorkflow(workflow.id)}
                          disabled={workflow.status === 'running'}
                        >
                          <Play className="w-4 h-4" />
                        </Button>
                        <Button size="sm" variant="outline">
                          <Edit className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>

                    {/* Workflow Steps Preview */}
                    <div className="flex items-center gap-2 text-sm">
                      {workflow.steps.slice(0, 4).map((step, index) => (
                        <React.Fragment key={step.id}>
                          <div className="flex items-center gap-1">
                            {getStepIcon(step.type)}
                            <span className="text-xs">{step.name}</span>
                          </div>
                          {index < Math.min(workflow.steps.length - 1, 3) && (
                            <ArrowRight className="w-3 h-3 text-muted-foreground" />
                          )}
                        </React.Fragment>
                      ))}
                      {workflow.steps.length > 4 && (
                        <span className="text-xs text-muted-foreground">
                          +{workflow.steps.length - 4} more
                        </span>
                      )}
                    </div>

                    {workflow.lastRun && (
                      <div className="text-xs text-muted-foreground mt-2">
                        Last run: {workflow.lastRun.toLocaleString()}
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Recent Executions */}
          {executions.length > 0 && (
            <div>
              <h3 className="font-medium mb-3">Recent Executions</h3>
              <ScrollArea className="h-64">
                <div className="space-y-2">
                  {executions.slice(0, 5).map((execution) => {
                    const workflow = workflows.find(w => w.id === execution.workflowId);
                    return (
                      <Card key={execution.id}>
                        <CardContent className="pt-3">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center gap-2">
                              <span className="font-medium text-sm">{workflow?.name}</span>
                              {getStatusIcon(execution.status)}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {execution.startTime.toLocaleTimeString()}
                            </div>
                          </div>
                          
                          {/* Step Progress */}
                          <div className="space-y-1">
                            {execution.stepResults.map((result, index) => (
                              <div key={result.stepId} className="flex items-center gap-2 text-xs">
                                {getStatusIcon(result.status)}
                                <span className={result.status === 'running' ? 'font-medium' : ''}>
                                  {workflow?.steps[index]?.name}
                                </span>
                                {result.output && (
                                  <span className="text-muted-foreground ml-auto">
                                    {result.output}
                                  </span>
                                )}
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              </ScrollArea>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}