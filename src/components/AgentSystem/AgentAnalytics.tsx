import { memo, useMemo } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { AgentMetrics } from './types';
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar
} from 'recharts';
import {
  TrendingUp,
  TrendingDown,
  Activity,
  CheckCircle,
  XCircle,
  Clock,
  Cpu,
  HardDrive,
  Zap
} from 'lucide-react';

interface AgentAnalyticsProps {
  metrics: AgentMetrics | null;
}

const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];

export const AgentAnalytics = memo(function AgentAnalytics({ metrics }: AgentAnalyticsProps) {
  const performanceData = useMemo(() => {
    if (!metrics) return [];
    
    return metrics.performance.hourly.slice(-24).map((value, index) => ({
      hour: `${index}:00`,
      tasks: value
    }));
  }, [metrics]);

  const taskDistribution = useMemo(() => {
    if (!metrics) return [];
    
    return [
      { name: 'Completed', value: metrics.completedTasks },
      { name: 'Failed', value: metrics.failedTasks },
      { name: 'Pending', value: metrics.totalTasks - metrics.completedTasks - metrics.failedTasks }
    ];
  }, [metrics]);

  const efficiencyData = useMemo(() => {
    if (!metrics) return [];
    
    return [
      { metric: 'Accuracy', value: metrics.accuracy, fullMark: 100 },
      { metric: 'Efficiency', value: metrics.efficiency, fullMark: 100 },
      { metric: 'Speed', value: Math.min(100, (1000 / metrics.averageCompletionTime) * 100), fullMark: 100 },
      { metric: 'Resource Usage', value: 100 - metrics.resourceUsage.cpu, fullMark: 100 }
    ];
  }, [metrics]);

  if (!metrics) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card className="col-span-full">
          <CardContent className="flex items-center justify-center h-32">
            <p className="text-muted-foreground">No analytics data available</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const successRate = ((metrics.completedTasks / metrics.totalTasks) * 100) || 0;
  const trend = metrics.performance.daily[6] > metrics.performance.daily[0] ? 'up' : 'down';

  return (
    <div className="space-y-4">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center justify-between">
              Total Tasks
              <Activity className="w-4 h-4 text-muted-foreground" />
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.totalTasks}</div>
            <div className="flex items-center gap-1 mt-1">
              {trend === 'up' ? (
                <TrendingUp className="w-4 h-4 text-green-500" />
              ) : (
                <TrendingDown className="w-4 h-4 text-red-500" />
              )}
              <span className="text-xs text-muted-foreground">
                {Math.abs(metrics.performance.daily[6] - metrics.performance.daily[0])} from last week
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center justify-between">
              Success Rate
              <CheckCircle className="w-4 h-4 text-muted-foreground" />
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{successRate.toFixed(1)}%</div>
            <Progress value={successRate} className="mt-2 h-1" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center justify-between">
              Avg Completion Time
              <Clock className="w-4 h-4 text-muted-foreground" />
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {metrics.averageCompletionTime < 60000
                ? `${(metrics.averageCompletionTime / 1000).toFixed(1)}s`
                : `${(metrics.averageCompletionTime / 60000).toFixed(1)}m`}
            </div>
            <p className="text-xs text-muted-foreground mt-1">Per task</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center justify-between">
              Resource Usage
              <Cpu className="w-4 h-4 text-muted-foreground" />
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-xs">CPU</span>
                <span className="text-xs font-medium">{metrics.resourceUsage.cpu}%</span>
              </div>
              <Progress value={metrics.resourceUsage.cpu} className="h-1" />
              <div className="flex items-center justify-between">
                <span className="text-xs">Memory</span>
                <span className="text-xs font-medium">{metrics.resourceUsage.memory}%</span>
              </div>
              <Progress value={metrics.resourceUsage.memory} className="h-1" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Task Performance Over Time */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Task Performance (24h)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={performanceData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="hour" />
                  <YAxis />
                  <Tooltip />
                  <Line
                    type="monotone"
                    dataKey="tasks"
                    stroke="#3B82F6"
                    strokeWidth={2}
                    dot={false}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Task Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Task Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={taskDistribution}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ percentage }) => percentage ? `${percentage.toFixed(0)}%` : ''}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {taskDistribution.map((_entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Efficiency Radar */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Agent Efficiency</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <RadarChart data={efficiencyData}>
                  <PolarGrid />
                  <PolarAngleAxis dataKey="metric" />
                  <PolarRadiusAxis angle={90} domain={[0, 100]} />
                  <Radar
                    name="Performance"
                    dataKey="value"
                    stroke="#3B82F6"
                    fill="#3B82F6"
                    fillOpacity={0.6}
                  />
                  <Tooltip />
                </RadarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Token Usage */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Token Usage</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm">Total Tokens Used</span>
                  <span className="text-sm font-medium">{metrics.resourceUsage.tokens.toLocaleString()}</span>
                </div>
                <Progress value={Math.min(100, (metrics.resourceUsage.tokens / 100000) * 100)} />
              </div>
              
              <div className="grid grid-cols-2 gap-4 pt-2">
                <div>
                  <p className="text-xs text-muted-foreground">Tokens per Task</p>
                  <p className="text-lg font-semibold">
                    {Math.round(metrics.resourceUsage.tokens / metrics.totalTasks)}
                  </p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">Est. Daily Usage</p>
                  <p className="text-lg font-semibold">
                    {Math.round((metrics.resourceUsage.tokens / 7) * 1).toLocaleString()}
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
});