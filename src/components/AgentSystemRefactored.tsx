import { useState, useEffect, memo } from 'react';
import { <PERSON>, CardHeader, CardT<PERSON>le, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Slider } from '@/components/ui/slider';
// import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { api } from '@/lib/api';
// import { motion } from 'framer-motion';
import { <PERSON><PERSON>, XCircle, AlertCircle, Split, Save, CheckCircle } from 'lucide-react';
import { useAgentSystemStore } from '@/stores/agentSystemStore';
import { useAgentSystemAutosave } from '@/hooks/useAgentSystemAutosave';

// Import modular components
import {
  AgentType,
  Agent,
  AgentTask,
  AgentConversation,
  AgentMetrics,
  AgentSettings,
  AgentTypesList,
  AgentList,
  TaskManager,
  AgentAnalytics
} from './AgentSystem/index';

// Type alias for conversation type
type ConversationType = AgentConversation;
import { AgentConversation as AgentConversationComponent } from './AgentSystem/AgentConversation';
import { BulkTaskManager } from './AgentSystem/BulkTaskManager';

interface AgentSystemProps {
  sessionId: string;
  projectPath: string;
  onClose?: () => void;
}

// Helper function to format time since last save
function formatTimeSince(timestamp: number): string {
  const seconds = Math.floor((Date.now() - timestamp) / 1000);
  
  if (seconds < 5) return 'just now';
  if (seconds < 60) return `${seconds}s ago`;
  
  const minutes = Math.floor(seconds / 60);
  if (minutes < 60) return `${minutes}m ago`;
  
  const hours = Math.floor(minutes / 60);
  if (hours < 24) return `${hours}h ago`;
  
  return 'over a day ago';
}

export const AgentSystem = memo(function AgentSystem({ sessionId, projectPath, onClose }: AgentSystemProps) {
  // Get state from store
  const {
    selectedAgent,
    selectedType,
    activeTab,
    agents,
    tasks,
    conversation,
    analytics,
    deployForm,
    setSelectedAgent,
    setSelectedType,
    setActiveTab,
    setAgents,
    setTasks,
    setConversation,
    setAnalytics,
    setDeployForm,
    updateDeployForm
  } = useAgentSystemStore();
  
  // Local state for UI-only concerns
  const [agentTypes, setAgentTypes] = useState<AgentType[]>([]);
  const [showDeployDialog, setShowDeployDialog] = useState(false);
  const [showConfigDialog, setShowConfigDialog] = useState(false);
  const [configTarget, setConfigTarget] = useState<Agent | null>(null);
  const [isLoading, setIsLoading] = useState(true);  // eslint-disable-line @typescript-eslint/no-unused-vars
  const [error, setError] = useState<string | null>(null);
  
  // Agent configuration form
  const [configForm, setConfigForm] = useState<AgentSettings>({
    autonomy: 'guided',
    creativity: 50,
    verbosity: 50,
    maxConcurrentTasks: 3,
    preferredTools: []
  });
  
  // Initialize autosave
  const { hasUnsavedChanges, lastSaved, performManualSave } = useAgentSystemAutosave();
  
  // Update the time display every second when there are no unsaved changes
  const [, forceUpdate] = useState(0);
  useEffect(() => {
    if (!hasUnsavedChanges) {
      const interval = setInterval(() => {
        forceUpdate(n => n + 1);
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [hasUnsavedChanges]);
  
  // Keyboard shortcut for manual save (Cmd/Ctrl + S)
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key === 's') {
        e.preventDefault();
        if (hasUnsavedChanges) {
          performManualSave();
        }
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [hasUnsavedChanges, performManualSave]);

  // Load initial data
  useEffect(() => {
    loadAgentTypes();
    loadAgents();
    loadTasks();
  }, [sessionId, projectPath]);
  
  // Update analytics when selected agent changes
  useEffect(() => {
    if (selectedAgent) {
      loadAgentAnalytics(selectedAgent.id);
      loadAgentConversation(selectedAgent.id);
    }
  }, [selectedAgent]);

  const loadAgentTypes = async () => {
    try {
      // Use mock data since API doesn't have this method yet
      const types = getMockAgentTypes();
      setAgentTypes(types);
    } catch (error) {
      console.error('Failed to load agent types:', error);
      // Use mock data
      setAgentTypes(getMockAgentTypes());
    }
  };

  const loadAgents = async () => {
    try {
      const agentList = await api.getAgents(sessionId);
      setAgents(agentList);
    } catch (error) {
      console.error('Failed to load agents:', error);
      // Use mock data
      setAgents(getMockAgents());
    } finally {
      setIsLoading(false);
    }
  };

  const loadTasks = async () => {
    try {
      const taskList = await api.getAgentTasks(sessionId);
      setTasks(taskList);
    } catch (error) {
      console.error('Failed to load tasks:', error);
      setTasks(getMockTasks());
    }
  };

  const loadAgentAnalytics = async (agentId: string) => {
    try {
      // Use mock data since API doesn't have this method yet
      const metrics = getMockAnalytics();
      setAnalytics(metrics);
    } catch (error) {
      console.error('Failed to load agent analytics:', error);
      setAnalytics(getMockAnalytics());
    }
  };

  const loadAgentConversation = async (agentId: string) => {
    try {
      // Use mock data since API doesn't have this method yet
      const conv = getMockConversation(agentId);
      setConversation(conv);
    } catch (error) {
      console.error('Failed to load conversation:', error);
      setConversation(getMockConversation(agentId));
    }
  };

  // Event handlers
  const handleDeployAgent = async () => {
    if (!selectedType || !deployForm.name) return;
    
    try {
      // Mock agent deployment since API doesn't have this method yet
      const newAgent: Agent = {
        id: `agent-${Date.now()}`,
        name: deployForm.name,
        type: selectedType,
        status: 'idle',
        tasksCompleted: 0,
        accuracy: 95,
        lastActive: new Date(),
        memory: { shortTerm: [], longTerm: {}, workingMemory: {} },
        settings: { autonomy: 'guided', creativity: 50, verbosity: 50, maxConcurrentTasks: 3, preferredTools: [] }
      };
      
      setAgents([...agents, newAgent]);
      setShowDeployDialog(false);
      setDeployForm({ name: '', specialization: '' });
      setSelectedType(null);
    } catch (error) {
      console.error('Failed to deploy agent:', error);
      setError('Failed to deploy agent');
    }
  };

  const handleConfigureAgent = (agent: Agent) => {
    setConfigTarget(agent);
    setConfigForm(agent.settings);
    setShowConfigDialog(true);
  };

  const handleUpdateAgentConfig = async () => {
    if (!configTarget) return;
    
    try {
      // Mock update since API doesn't have this method yet
      console.log('Updating agent settings:', configTarget.id, configForm);
      setAgents(agents.map(a => 
        a.id === configTarget.id 
          ? { ...a, settings: configForm }
          : a
      ));
      setShowConfigDialog(false);
      setConfigTarget(null);
    } catch (error) {
      console.error('Failed to update agent configuration:', error);
    }
  };

  const handlePauseAgent = async (agentId: string) => {
    try {
      // Mock pause since API doesn't have this method yet
      console.log('Pausing agent:', agentId);
      setAgents(agents.map(a => 
        a.id === agentId ? { ...a, status: 'paused' } : a
      ));
    } catch (error) {
      console.error('Failed to pause agent:', error);
    }
  };

  const handleResumeAgent = async (agentId: string) => {
    try {
      // Mock resume since API doesn't have this method yet
      console.log('Resuming agent:', agentId);
      setAgents(agents.map(a => 
        a.id === agentId ? { ...a, status: 'idle' } : a
      ));
    } catch (error) {
      console.error('Failed to resume agent:', error);
    }
  };

  const handleRemoveAgent = async (agentId: string) => {
    try {
      // Mock remove since API doesn't have this method yet
      console.log('Removing agent:', agentId);
      setAgents(agents.filter(a => a.id !== agentId));
      if (selectedAgent?.id === agentId) {
        setSelectedAgent(null);
      }
    } catch (error) {
      console.error('Failed to remove agent:', error);
    }
  };

  const handleCreateTask = async (task: Partial<AgentTask>) => {
    try {
      // Mock task creation since API doesn't have this method yet
      const newTask: AgentTask = {
        id: `task-${Date.now()}`,
        agentId: task.agentId || '',
        title: task.title || '',
        description: task.description || '',
        priority: task.priority || 'medium',
        status: 'pending',
        progress: 0,
        createdAt: new Date(),
        dependencies: [],
        subtasks: []
      };
      setTasks([...tasks, newTask]);
    } catch (error) {
      console.error('Failed to create task:', error);
    }
  };

  const handleSendMessage = async (message: string) => {
    if (!selectedAgent || !conversation) return;
    
    try {
      // Mock message sending since API doesn't have this method yet
      const response = {
        userMessage: {
          id: `msg-${Date.now()}`,
          role: 'user' as const,
          content: message,
          timestamp: new Date()
        },
        agentMessage: {
          id: `msg-${Date.now() + 1}`,
          role: 'agent' as const,
          content: `I received your message: "${message}"`,
          timestamp: new Date()
        }
      };
      setConversation({
        ...conversation,
        messages: [...conversation.messages, response.userMessage, response.agentMessage]
      });
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  const handleSelectType = (type: AgentType) => {
    setSelectedType(type.id);
    setShowDeployDialog(true);
  };

  return (
    <div className="h-full flex flex-col">
      <Card className="flex-1 flex flex-col">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-4">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Bot className="w-5 h-5" />
                    AI Agent System
                  </CardTitle>
                  <p className="text-sm text-muted-foreground mt-1">
                    Deploy and manage intelligent agents for your project
                  </p>
                </div>
                {/* Save indicator */}
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  {hasUnsavedChanges ? (
                    <>
                      <Save className="w-4 h-4 text-orange-500" />
                      <span>Unsaved changes</span>
                      <span className="text-xs">(Cmd/Ctrl+S to save)</span>
                    </>
                  ) : (
                    <>
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span>All changes saved</span>
                    </>
                  )}
                </div>
              </div>
            </div>
            {onClose && (
              <Button variant="ghost" size="icon" onClick={onClose}>
                <XCircle className="w-4 h-4" />
              </Button>
            )}
          </div>
        </CardHeader>
        
        <CardContent className="flex-1 overflow-hidden">
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="catalog">Agent Catalog</TabsTrigger>
              <TabsTrigger value="deployed">Deployed Agents</TabsTrigger>
              <TabsTrigger value="tasks">Task Manager</TabsTrigger>
              <TabsTrigger value="bulk-tasks">Bulk Tasks</TabsTrigger>
              <TabsTrigger value="conversation">Conversation</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
            </TabsList>
            
            <TabsContent value="catalog" className="flex-1 overflow-auto mt-4">
              <AgentTypesList
                agentTypes={agentTypes}
                selectedType={selectedType}
                onSelectType={handleSelectType}
              />
            </TabsContent>
            
            <TabsContent value="deployed" className="flex-1 overflow-auto mt-4">
              <AgentList
                agents={agents}
                selectedAgent={selectedAgent}
                onSelectAgent={setSelectedAgent}
                onConfigureAgent={handleConfigureAgent}
                onPauseAgent={handlePauseAgent}
                onResumeAgent={handleResumeAgent}
                onRemoveAgent={handleRemoveAgent}
              />
            </TabsContent>
            
            <TabsContent value="tasks" className="flex-1 overflow-hidden mt-4">
              <TaskManager
                tasks={tasks}
                agents={agents}
                onCreateTask={handleCreateTask}
                onUpdateTaskStatus={async () => {}} // Implement if needed
                onAssignTask={async () => {}} // Implement if needed
              />
            </TabsContent>
            
            <TabsContent value="conversation" className="flex-1 overflow-hidden mt-4">
              <AgentConversationComponent
                agent={selectedAgent}
                conversation={conversation}
                onSendMessage={handleSendMessage}
                onClearConversation={() => setConversation(null)}
              />
            </TabsContent>
            
            <TabsContent value="analytics" className="flex-1 overflow-auto mt-4">
              <AgentAnalytics metrics={analytics} />
            </TabsContent>
            
            <TabsContent value="bulk-tasks" className="flex-1 mt-4">
              <BulkTaskManager />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
      
      {/* Deploy Agent Dialog */}
      <Dialog open={showDeployDialog} onOpenChange={setShowDeployDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Deploy New Agent</DialogTitle>
            <DialogDescription>
              Configure and deploy a new AI agent to your system
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div>
              <Label htmlFor="agent-name">Agent Name</Label>
              <Input
                id="agent-name"
                value={deployForm.name}
                onChange={(e) => updateDeployForm({ name: e.target.value })}
                placeholder="Enter agent name"
              />
            </div>
            <div>
              <Label htmlFor="agent-spec">Specialization (Optional)</Label>
              <Input
                id="agent-spec"
                value={deployForm.specialization}
                onChange={(e) => updateDeployForm({ specialization: e.target.value })}
                placeholder="e.g., React development, API design"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeployDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleDeployAgent} disabled={!deployForm.name}>
              Deploy Agent
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Configure Agent Dialog */}
      <Dialog open={showConfigDialog} onOpenChange={setShowConfigDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Configure Agent</DialogTitle>
            <DialogDescription>
              Adjust agent settings and behavior
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div>
              <Label htmlFor="autonomy">Autonomy Level</Label>
              <Select
                value={configForm.autonomy}
                onValueChange={(v) => setConfigForm({ ...configForm, autonomy: v as any })}
              >
                <SelectTrigger id="autonomy">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="manual">Manual</SelectItem>
                  <SelectItem value="guided">Guided</SelectItem>
                  <SelectItem value="full">Full Autonomy</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <div className="flex items-center justify-between mb-2">
                <Label>Creativity</Label>
                <span className="text-sm text-muted-foreground">{configForm.creativity}%</span>
              </div>
              <Slider
                value={[configForm.creativity]}
                onValueChange={([v]) => setConfigForm({ ...configForm, creativity: v })}
                min={0}
                max={100}
                step={10}
              />
            </div>
            
            <div>
              <div className="flex items-center justify-between mb-2">
                <Label>Verbosity</Label>
                <span className="text-sm text-muted-foreground">{configForm.verbosity}%</span>
              </div>
              <Slider
                value={[configForm.verbosity]}
                onValueChange={([v]) => setConfigForm({ ...configForm, verbosity: v })}
                min={0}
                max={100}
                step={10}
              />
            </div>
            
            <div>
              <Label htmlFor="max-tasks">Max Concurrent Tasks</Label>
              <Input
                id="max-tasks"
                type="number"
                value={configForm.maxConcurrentTasks}
                onChange={(e) => setConfigForm({ ...configForm, maxConcurrentTasks: parseInt(e.target.value) || 1 })}
                min={1}
                max={10}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowConfigDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateAgentConfig}>
              Save Configuration
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
});

// Mock data generators
function getMockAgentTypes(): AgentType[] {
  return [
    {
      id: 'code-reviewer',
      name: 'Code Reviewer',
      description: 'Reviews code for quality, security, and best practices',
      category: 'code',
      capabilities: ['Code analysis', 'Security scanning', 'Performance optimization'],
      limitations: ['Cannot modify code directly', 'Limited to supported languages'],
      cost: 100,
      speed: 'fast',
      accuracy: 'high'
    },
    {
      id: 'bug-hunter',
      name: 'Bug Hunter',
      description: 'Identifies and diagnoses bugs in your codebase',
      category: 'testing',
      capabilities: ['Error detection', 'Root cause analysis', 'Fix suggestions'],
      limitations: ['Requires test data', 'May have false positives'],
      cost: 150,
      speed: 'medium',
      accuracy: 'high'
    },
    {
      id: 'doc-writer',
      name: 'Documentation Writer',
      description: 'Generates and maintains project documentation',
      category: 'documentation',
      capabilities: ['API documentation', 'README generation', 'Code comments'],
      limitations: ['Requires code context', 'Language style is formal'],
      cost: 80,
      speed: 'fast',
      accuracy: 'medium'
    }
  ];
}

function getMockAgents(): Agent[] {
  return [
    {
      id: 'agent-1',
      name: 'CodeBot',
      type: 'code-reviewer',
      status: 'working',
      currentTask: 'Reviewing authentication module',
      tasksCompleted: 42,
      accuracy: 95,
      lastActive: new Date(),
      memory: {
        shortTerm: [],
        longTerm: {},
        workingMemory: {}
      },
      settings: {
        autonomy: 'guided',
        creativity: 30,
        verbosity: 50,
        maxConcurrentTasks: 3,
        preferredTools: []
      }
    }
  ];
}

function getMockTasks(): AgentTask[] {
  return [
    {
      id: 'task-1',
      agentId: 'agent-1',
      title: 'Review authentication module',
      description: 'Check for security vulnerabilities and code quality',
      priority: 'high',
      status: 'in-progress',
      progress: 65,
      createdAt: new Date(),
      dependencies: [],
      subtasks: []
    }
  ];
}

function getMockAnalytics(): AgentMetrics {
  return {
    totalTasks: 142,
    completedTasks: 128,
    failedTasks: 5,
    averageCompletionTime: 45000,
    accuracy: 95,
    efficiency: 88,
    resourceUsage: {
      cpu: 45,
      memory: 62,
      tokens: 48500
    },
    performance: {
      hourly: Array(24).fill(0).map(() => Math.floor(Math.random() * 10)),
      daily: Array(7).fill(0).map(() => Math.floor(Math.random() * 50)),
      weekly: Array(4).fill(0).map(() => Math.floor(Math.random() * 200))
    }
  };
}

function getMockConversation(agentId: string): ConversationType {
  return {
    id: `conv-${agentId}`,
    agentId,
    messages: [],
    context: {},
    startedAt: new Date(),
    lastMessageAt: new Date()
  };
}