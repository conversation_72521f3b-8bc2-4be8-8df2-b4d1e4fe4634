import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Code2, 
  Users, 
  FileText, 
  Cpu, 
  <PERSON>uzzle, 
  Bot,
  Sparkles,
  Zap,
  Shield,
  Workflow,
  Split,
  Brain
} from 'lucide-react';

interface Feature {
  id: string;
  title: string;
  description: string;
  icon: React.ElementType;
  category: string;
  priority?: number;
}

interface DynamicNavigationCardsProps {
  visibleFeatures: string[];
  suggestedFeatures: string[];
  onFeatureClick?: (featureId: string) => void;
}

const FEATURE_MAP: Record<string, Feature> = {
  projects: {
    id: 'projects',
    title: 'CC Projects',
    description: 'Browse and manage your Claude Code sessions',
    icon: Code2,
    category: 'core',
    priority: 10
  },
  agents: {
    id: 'agents',
    title: 'CC Agents',
    description: 'Specialized AI agents for development tasks',
    icon: Bot,
    category: 'core',
    priority: 9
  },
  settings: {
    id: 'settings',
    title: 'Settings',
    description: 'Configure application preferences',
    icon: Shield,
    category: 'core',
    priority: 8
  },
  mcp: {
    id: 'mcp',
    title: 'MCP Manager',
    description: 'Manage Model Context Protocol servers',
    icon: Cpu,
    category: 'core',
    priority: 7
  },
  templates: {
    id: 'templates',
    title: 'Smart Templates',
    description: 'Reusable code templates with variables',
    icon: FileText,
    category: 'productivity'
  },
  performance: {
    id: 'performance',
    title: 'Performance Profiler',
    description: 'Monitor API usage and optimize performance',
    icon: Zap,
    category: 'monitoring'
  },
  plugins: {
    id: 'plugins',
    title: 'Plugin Manager',
    description: 'Extend functionality with custom plugins',
    icon: Puzzle,
    category: 'extensibility'
  },
  'agent-system': {
    id: 'agent-system',
    title: 'Agent System',
    description: 'Intelligent AI agents for complex tasks',
    icon: Sparkles,
    category: 'ai'
  },
  'parallel-agents': {
    id: 'parallel-agents',
    title: 'Parallel Agents',
    description: 'Massive parallel AI processing with auto-scaling',
    icon: Split,
    category: 'ai',
    priority: 8
  },
  collaboration: {
    id: 'collaboration',
    title: 'Real-time Collaboration',
    description: 'Work together on code in real-time',
    icon: Users,
    category: 'collaboration'
  },
  workflow: {
    id: 'workflow',
    title: 'Workflow Automation',
    description: 'Automate repetitive development tasks',
    icon: Workflow,
    category: 'automation'
  },
  'bulk-tasks': {
    id: 'bulk-tasks',
    title: 'Bulk Task Manager',
    description: 'Process 5000+ tasks with AI agent delegation',
    icon: Split,
    category: 'ai',
    priority: 6
  },
  'enhanced-agents': {
    id: 'enhanced-agents',
    title: 'Enhanced Agent Dashboard',
    description: 'Advanced agent monitoring and collaboration',
    icon: Users,
    category: 'ai',
    priority: 7
  },
  'visual-effects': {
    id: 'visual-effects',
    title: 'Visual Effects Manager',
    description: 'Customize themes, animations, and visual effects',
    icon: Sparkles,
    category: 'customization',
    priority: 5
  },
  'agent-execution': {
    id: 'agent-execution',
    title: 'Agent Execution Demo',
    description: 'Interactive demo of agent execution and tools',
    icon: Bot,
    category: 'demo',
    priority: 4
  },
  'smart-assistant': {
    id: 'smart-assistant',
    title: 'Smart Code Assistant',
    description: 'AI-powered code suggestions and optimizations',
    icon: Brain,
    category: 'productivity',
    priority: 6
  },
  'smart-features': {
    id: 'smart-features',
    title: 'Smart Feature Selection',
    description: 'Intelligent feature recommendations and management',
    icon: Sparkles,
    category: 'productivity',
    priority: 5
  },
  'workflow-automation': {
    id: 'workflow-automation',
    title: 'Workflow Automation',
    description: 'Automate development workflows and tasks',
    icon: Workflow,
    category: 'automation',
    priority: 7
  },
  'enhanced-mcp': {
    id: 'enhanced-mcp',
    title: 'Enhanced MCP Manager',
    description: 'Advanced MCP server management with monitoring',
    icon: Cpu,
    category: 'core',
    priority: 8
  }
};

const categoryColors: Record<string, string> = {
  core: 'bg-gray-500/10 text-gray-600 dark:text-gray-400',
  productivity: 'bg-blue-500/10 text-blue-500',
  monitoring: 'bg-purple-500/10 text-purple-500',
  extensibility: 'bg-green-500/10 text-green-500',
  ai: 'bg-amber-500/10 text-amber-500',
  collaboration: 'bg-pink-500/10 text-pink-500',
  automation: 'bg-indigo-500/10 text-indigo-500',
  customization: 'bg-violet-500/10 text-violet-500',
  demo: 'bg-cyan-500/10 text-cyan-500'
};

export function DynamicNavigationCards({ 
  visibleFeatures, 
  suggestedFeatures,
  onFeatureClick 
}: DynamicNavigationCardsProps) {
  // Ensure we show all available features from FEATURE_MAP that are in visibleFeatures
  const features = Object.values(FEATURE_MAP)
    .filter(feature => visibleFeatures.includes(feature.id))
    .sort((a, b) => (b.priority || 0) - (a.priority || 0));

  if (features.length === 0) return null;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
      {features.map((feature, index) => {
        const Icon = feature.icon;
        const isSuggested = suggestedFeatures.includes(feature.id);
        const isNew = suggestedFeatures.includes(feature.id) && !visibleFeatures.includes(feature.id);

        return (
          <motion.div
            key={feature.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card 
              className="hover:shadow-lg transition-all cursor-pointer group relative overflow-hidden"
              onClick={() => onFeatureClick?.(feature.id)}
            >
              {isNew && (
                <div className="absolute top-2 right-2">
                  <Badge variant="secondary" className="bg-gradient-to-r from-purple-500 to-pink-500 text-white border-0">
                    <Sparkles className="w-3 h-3 mr-1" />
                    New
                  </Badge>
                </div>
              )}
              
              {isSuggested && !isNew && (
                <div className="absolute top-2 right-2">
                  <Badge variant="outline" className="border-amber-500/50 text-amber-600">
                    <Zap className="w-3 h-3 mr-1" />
                    Suggested
                  </Badge>
                </div>
              )}

              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="p-2 rounded-lg bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 group-hover:scale-110 transition-transform">
                    <Icon className="w-6 h-6 text-gray-700 dark:text-gray-300" />
                  </div>
                </div>
                <CardTitle className="text-lg mt-3">{feature.title}</CardTitle>
              </CardHeader>
              
              <CardContent>
                <CardDescription className="text-sm">{feature.description}</CardDescription>
                <div className="mt-3">
                  <Badge 
                    variant="secondary" 
                    className={`${categoryColors[feature.category]} border-0`}
                  >
                    {feature.category}
                  </Badge>
                </div>
              </CardContent>

              <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-gray-200 to-transparent dark:via-gray-700 opacity-0 group-hover:opacity-100 transition-opacity" />
            </Card>
          </motion.div>
        );
      })}
    </div>
  );
}