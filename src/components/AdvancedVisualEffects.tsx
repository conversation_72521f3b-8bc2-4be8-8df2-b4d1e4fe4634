import React, { useState, useEffect, useRef, useMemo } from 'react';
import { motion, AnimatePresence, useAnimation, useMotionValue, useTransform } from 'framer-motion';
import {
  <PERSON>rk<PERSON>,
  Zap,
  Eye,
  Palette,
  Waves,
  Star,
  Circle,
  Triangle,
  Square,
  Heart,
  Hexagon,
  Play,
  Pause,
  Volume2,
  VolumeX,
  Monitor,
  Smartphone,
  Tablet,
  Gamepad2,
  Cpu,
  Activity,
  BarChart3,
  TrendingUp,
  Layers,
  Maximize,
  Minimize,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';

interface AdvancedVisualEffectsProps {
  className?: string;
  onEffectChange?: (effects: VisualEffectSettings) => void;
}

interface VisualEffectSettings {
  theme: 'cyberpunk' | 'neon' | 'glass' | 'retro' | 'minimal' | 'cosmic';
  animations: {
    enabled: boolean;
    intensity: number;
    speed: number;
    easing: 'linear' | 'ease' | 'bounce' | 'elastic';
  };
  particles: {
    enabled: boolean;
    count: number;
    size: number;
    speed: number;
    color: string;
    shape: 'circle' | 'square' | 'triangle' | 'star';
  };
  lighting: {
    enabled: boolean;
    intensity: number;
    color: string;
    shadows: boolean;
    reflections: boolean;
  };
  distortions: {
    enabled: boolean;
    glitch: boolean;
    wave: boolean;
    zoom: boolean;
    rotation: boolean;
  };
  interactive: {
    mouseEffects: boolean;
    clickRipples: boolean;
    hoverGlow: boolean;
    parallax: boolean;
  };
  performance: {
    quality: 'low' | 'medium' | 'high' | 'ultra';
    fps: number;
    gpuAcceleration: boolean;
  };
}

// Advanced particle system with multiple shapes and behaviors
const AdvancedParticleSystem: React.FC<{
  settings: VisualEffectSettings['particles'];
  theme: string;
}> = ({ settings, theme }) => {
  const [particles, setParticles] = useState<Array<{
    id: number;
    x: number;
    y: number;
    vx: number;
    vy: number;
    size: number;
    color: string;
    shape: string;
    rotation: number;
    rotationSpeed: number;
    life: number;
    maxLife: number;
  }>>([]);

  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();

  useEffect(() => {
    if (!settings.enabled || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;

    // Initialize particles
    const newParticles = Array.from({ length: settings.count }, (_, i) => ({
      id: i,
      x: Math.random() * canvas.width,
      y: Math.random() * canvas.height,
      vx: (Math.random() - 0.5) * settings.speed,
      vy: (Math.random() - 0.5) * settings.speed,
      size: Math.random() * settings.size + 2,
      color: settings.color,
      shape: settings.shape,
      rotation: Math.random() * Math.PI * 2,
      rotationSpeed: (Math.random() - 0.5) * 0.1,
      life: Math.random() * 1000,
      maxLife: 1000,
    }));

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      newParticles.forEach((particle, index) => {
        // Update position
        particle.x += particle.vx;
        particle.y += particle.vy;
        particle.rotation += particle.rotationSpeed;
        particle.life += 1;

        // Wrap around screen
        if (particle.x < 0) particle.x = canvas.width;
        if (particle.x > canvas.width) particle.x = 0;
        if (particle.y < 0) particle.y = canvas.height;
        if (particle.y > canvas.height) particle.y = 0;

        // Reset particle if life exceeded
        if (particle.life > particle.maxLife) {
          particle.life = 0;
          particle.x = Math.random() * canvas.width;
          particle.y = Math.random() * canvas.height;
        }

        // Draw particle
        ctx.save();
        ctx.translate(particle.x, particle.y);
        ctx.rotate(particle.rotation);
        ctx.fillStyle = particle.color;
        ctx.globalAlpha = 1 - (particle.life / particle.maxLife);

        switch (particle.shape) {
          case 'circle':
            ctx.beginPath();
            ctx.arc(0, 0, particle.size, 0, Math.PI * 2);
            ctx.fill();
            break;
          case 'square':
            ctx.fillRect(-particle.size/2, -particle.size/2, particle.size, particle.size);
            break;
          case 'triangle':
            ctx.beginPath();
            ctx.moveTo(0, -particle.size);
            ctx.lineTo(-particle.size, particle.size);
            ctx.lineTo(particle.size, particle.size);
            ctx.closePath();
            ctx.fill();
            break;
          case 'star':
            drawStar(ctx, 0, 0, 5, particle.size, particle.size/2);
            ctx.fill();
            break;
        }
        ctx.restore();
      });

      animationRef.current = requestAnimationFrame(animate);
    };

    animationRef.current = requestAnimationFrame(animate);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [settings]);

  const drawStar = (ctx: CanvasRenderingContext2D, cx: number, cy: number, spikes: number, outerRadius: number, innerRadius: number) => {
    let rot = Math.PI / 2 * 3;
    let x = cx;
    let y = cy;
    const step = Math.PI / spikes;

    ctx.beginPath();
    ctx.moveTo(cx, cy - outerRadius);

    for (let i = 0; i < spikes; i++) {
      x = cx + Math.cos(rot) * outerRadius;
      y = cy + Math.sin(rot) * outerRadius;
      ctx.lineTo(x, y);
      rot += step;

      x = cx + Math.cos(rot) * innerRadius;
      y = cy + Math.sin(rot) * innerRadius;
      ctx.lineTo(x, y);
      rot += step;
    }

    ctx.lineTo(cx, cy - outerRadius);
    ctx.closePath();
  };

  if (!settings.enabled) return null;

  return (
    <canvas
      ref={canvasRef}
      className="fixed inset-0 pointer-events-none z-0"
      style={{ opacity: 0.6 }}
    />
  );
};

// Interactive mouse effects
const MouseEffects: React.FC<{ enabled: boolean }> = ({ enabled }) => {
  const [mousePos, setMousePos] = useState({ x: 0, y: 0 });
  const [clicks, setClicks] = useState<Array<{ id: number; x: number; y: number; time: number }>>([]);

  useEffect(() => {
    if (!enabled) return;

    const handleMouseMove = (e: MouseEvent) => {
      setMousePos({ x: e.clientX, y: e.clientY });
    };

    const handleClick = (e: MouseEvent) => {
      const newClick = {
        id: Date.now(),
        x: e.clientX,
        y: e.clientY,
        time: Date.now(),
      };
      setClicks(prev => [...prev, newClick]);

      // Remove click after animation
      setTimeout(() => {
        setClicks(prev => prev.filter(click => click.id !== newClick.id));
      }, 1000);
    };

    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('click', handleClick);

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('click', handleClick);
    };
  }, [enabled]);

  if (!enabled) return null;

  return (
    <div className="fixed inset-0 pointer-events-none z-10">
      {/* Mouse trail */}
      <motion.div
        className="absolute w-6 h-6 bg-primary/30 rounded-full blur-sm"
        animate={{
          x: mousePos.x - 12,
          y: mousePos.y - 12,
        }}
        transition={{ type: "spring", damping: 30, stiffness: 200 }}
      />

      {/* Click ripples */}
      {clicks.map((click) => (
        <motion.div
          key={click.id}
          className="absolute border-2 border-primary/50 rounded-full"
          style={{
            left: click.x - 25,
            top: click.y - 25,
          }}
          initial={{ width: 0, height: 0, opacity: 1 }}
          animate={{ width: 50, height: 50, opacity: 0 }}
          transition={{ duration: 1, ease: "easeOut" }}
        />
      ))}
    </div>
  );
};

// Glitch effect overlay
const GlitchEffect: React.FC<{ enabled: boolean; intensity: number }> = ({ enabled, intensity }) => {
  if (!enabled) return null;

  return (
    <motion.div
      className="fixed inset-0 pointer-events-none z-20 mix-blend-difference"
      animate={{
        x: [0, intensity, -intensity, 0],
        opacity: [0, 0.1, 0, 0.05, 0],
      }}
      transition={{
        duration: 0.2,
        repeat: Infinity,
        repeatDelay: Math.random() * 3 + 1,
      }}
    >
      <div className="w-full h-full bg-gradient-to-r from-red-500 via-transparent to-blue-500 opacity-20" />
    </motion.div>
  );
};

// Performance monitor
const PerformanceMonitor: React.FC<{ settings: VisualEffectSettings['performance'] }> = ({ settings }) => {
  const [fps, setFps] = useState(60);
  const [memoryUsage, setMemoryUsage] = useState(0);
  const [gpuUsage, setGpuUsage] = useState(0);

  useEffect(() => {
    let lastTime = performance.now();
    let frameCount = 0;

    const measureFPS = () => {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime - lastTime >= 1000) {
        setFps(Math.round((frameCount * 1000) / (currentTime - lastTime)));
        frameCount = 0;
        lastTime = currentTime;
      }

      // Simulate memory and GPU usage
      if ((performance as any).memory) {
        const memory = (performance as any).memory;
        setMemoryUsage(Math.round((memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100));
      }
      setGpuUsage(Math.random() * 30 + 20); // Simulated GPU usage

      requestAnimationFrame(measureFPS);
    };

    const animationId = requestAnimationFrame(measureFPS);

    return () => cancelAnimationFrame(animationId);
  }, []);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="fixed bottom-4 left-4 z-50"
    >
      <Card className="p-3 bg-background/90 backdrop-blur-sm border">
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Activity className="h-3 w-3" />
            <span className="text-xs font-medium">Performance</span>
          </div>
          
          <div className="space-y-1">
            <div className="flex items-center justify-between text-xs">
              <span>FPS</span>
              <Badge variant={fps >= 50 ? "default" : fps >= 30 ? "secondary" : "destructive"}>
                {fps}
              </Badge>
            </div>
            
            <div className="flex items-center justify-between text-xs">
              <span>Memory</span>
              <span>{memoryUsage}%</span>
            </div>
            <Progress value={memoryUsage} className="h-1" />
            
            <div className="flex items-center justify-between text-xs">
              <span>GPU</span>
              <span>{Math.round(gpuUsage)}%</span>
            </div>
            <Progress value={gpuUsage} className="h-1" />
            
            <div className="flex items-center justify-between text-xs">
              <span>Quality</span>
              <Badge variant="outline" className="text-xs">
                {settings.quality.toUpperCase()}
              </Badge>
            </div>
          </div>
        </div>
      </Card>
    </motion.div>
  );
};

// Main component
export const AdvancedVisualEffects: React.FC<AdvancedVisualEffectsProps> = ({
  className,
  onEffectChange,
}) => {
  const [settings, setSettings] = useState<VisualEffectSettings>({
    theme: 'cyberpunk',
    animations: {
      enabled: true,
      intensity: 50,
      speed: 50,
      easing: 'ease',
    },
    particles: {
      enabled: false,
      count: 50,
      size: 4,
      speed: 1,
      color: '#3b82f6',
      shape: 'circle',
    },
    lighting: {
      enabled: false,
      intensity: 50,
      color: '#ffffff',
      shadows: true,
      reflections: false,
    },
    distortions: {
      enabled: false,
      glitch: false,
      wave: false,
      zoom: false,
      rotation: false,
    },
    interactive: {
      mouseEffects: false,
      clickRipples: true,
      hoverGlow: true,
      parallax: false,
    },
    performance: {
      quality: 'medium',
      fps: 60,
      gpuAcceleration: true,
    },
  });

  const [isOpen, setIsOpen] = useState(false);
  const [showPerformanceMonitor, setShowPerformanceMonitor] = useState(false);

  const updateSettings = (updates: Partial<VisualEffectSettings>) => {
    const newSettings = { ...settings, ...updates };
    setSettings(newSettings);
    onEffectChange?.(newSettings);
  };

  const themePresets = {
    cyberpunk: {
      particles: { color: '#00ff41', shape: 'square' as const },
      lighting: { color: '#ff0080' },
    },
    neon: {
      particles: { color: '#ff1493', shape: 'circle' as const },
      lighting: { color: '#00ffff' },
    },
    glass: {
      particles: { color: '#ffffff', shape: 'circle' as const },
      lighting: { color: '#f0f8ff' },
    },
    retro: {
      particles: { color: '#ffa500', shape: 'square' as const },
      lighting: { color: '#ff69b4' },
    },
    minimal: {
      particles: { color: '#6b7280', shape: 'circle' as const },
      lighting: { color: '#ffffff' },
    },
    cosmic: {
      particles: { color: '#9333ea', shape: 'star' as const },
      lighting: { color: '#8b5cf6' },
    },
  };

  const applyThemePreset = (theme: keyof typeof themePresets) => {
    const preset = themePresets[theme];
    updateSettings({
      theme,
      particles: { ...settings.particles, ...preset.particles },
      lighting: { ...settings.lighting, ...preset.lighting },
    });
  };

  return (
    <>
      {/* Visual Effects */}
      <AdvancedParticleSystem settings={settings.particles} theme={settings.theme} />
      <MouseEffects enabled={settings.interactive.mouseEffects} />
      <GlitchEffect enabled={settings.distortions.glitch} intensity={settings.animations.intensity / 10} />
      
      {/* Performance Monitor */}
      {showPerformanceMonitor && <PerformanceMonitor settings={settings.performance} />}

      {/* Control Panel */}
      <motion.div
        className={`fixed top-4 right-20 z-50 ${className}`}
        initial={{ opacity: 0, x: 100 }}
        animate={{ opacity: 1, x: 0 }}
      >
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsOpen(!isOpen)}
          className="mb-2 bg-background/80 backdrop-blur-sm"
        >
          <Zap className="h-4 w-4 mr-1" />
          Advanced FX
        </Button>

        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: -20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: -20 }}
              transition={{ type: "spring", damping: 20, stiffness: 300 }}
            >
              <Card className="w-80 bg-background/95 backdrop-blur-md border shadow-xl">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center justify-between">
                    <span className="flex items-center gap-2">
                      <Sparkles className="h-4 w-4" />
                      Advanced Visual Effects
                    </span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowPerformanceMonitor(!showPerformanceMonitor)}
                    >
                      <Activity className="h-3 w-3" />
                    </Button>
                  </CardTitle>
                </CardHeader>
                
                <CardContent>
                  <Tabs defaultValue="themes" className="w-full">
                    <TabsList className="grid w-full grid-cols-4 text-xs">
                      <TabsTrigger value="themes">Themes</TabsTrigger>
                      <TabsTrigger value="particles">Particles</TabsTrigger>
                      <TabsTrigger value="effects">Effects</TabsTrigger>
                      <TabsTrigger value="performance">Performance</TabsTrigger>
                    </TabsList>

                    <TabsContent value="themes" className="space-y-3">
                      <div className="grid grid-cols-2 gap-2">
                        {Object.keys(themePresets).map((theme) => (
                          <Button
                            key={theme}
                            variant={settings.theme === theme ? "default" : "outline"}
                            size="sm"
                            onClick={() => applyThemePreset(theme as keyof typeof themePresets)}
                            className="text-xs capitalize"
                          >
                            {theme}
                          </Button>
                        ))}
                      </div>
                    </TabsContent>

                    <TabsContent value="particles" className="space-y-3">
                      <div className="flex items-center justify-between">
                        <Label className="text-xs">Enable Particles</Label>
                        <Switch
                          checked={settings.particles.enabled}
                          onCheckedChange={(enabled) =>
                            updateSettings({
                              particles: { ...settings.particles, enabled }
                            })
                          }
                        />
                      </div>

                      {settings.particles.enabled && (
                        <>
                          <div>
                            <Label className="text-xs">Count: {settings.particles.count}</Label>
                            <Slider
                              value={[settings.particles.count]}
                              onValueChange={([count]) =>
                                updateSettings({
                                  particles: { ...settings.particles, count }
                                })
                              }
                              max={200}
                              min={10}
                              step={10}
                              className="mt-1"
                            />
                          </div>

                          <div>
                            <Label className="text-xs">Size: {settings.particles.size}</Label>
                            <Slider
                              value={[settings.particles.size]}
                              onValueChange={([size]) =>
                                updateSettings({
                                  particles: { ...settings.particles, size }
                                })
                              }
                              max={20}
                              min={1}
                              step={1}
                              className="mt-1"
                            />
                          </div>

                          <div>
                            <Label className="text-xs">Shape</Label>
                            <Select
                              value={settings.particles.shape}
                              onValueChange={(shape: 'circle' | 'square' | 'triangle' | 'star') =>
                                updateSettings({
                                  particles: { ...settings.particles, shape }
                                })
                              }
                            >
                              <SelectTrigger className="h-8 text-xs">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="circle">Circle</SelectItem>
                                <SelectItem value="square">Square</SelectItem>
                                <SelectItem value="triangle">Triangle</SelectItem>
                                <SelectItem value="star">Star</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </>
                      )}
                    </TabsContent>

                    <TabsContent value="effects" className="space-y-3">
                      <div className="flex items-center justify-between">
                        <Label className="text-xs">Mouse Effects</Label>
                        <Switch
                          checked={settings.interactive.mouseEffects}
                          onCheckedChange={(mouseEffects) =>
                            updateSettings({
                              interactive: { ...settings.interactive, mouseEffects }
                            })
                          }
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <Label className="text-xs">Glitch Effect</Label>
                        <Switch
                          checked={settings.distortions.glitch}
                          onCheckedChange={(glitch) =>
                            updateSettings({
                              distortions: { ...settings.distortions, glitch }
                            })
                          }
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <Label className="text-xs">Enhanced Animations</Label>
                        <Switch
                          checked={settings.animations.enabled}
                          onCheckedChange={(enabled) =>
                            updateSettings({
                              animations: { ...settings.animations, enabled }
                            })
                          }
                        />
                      </div>

                      {settings.animations.enabled && (
                        <div>
                          <Label className="text-xs">Intensity: {settings.animations.intensity}%</Label>
                          <Slider
                            value={[settings.animations.intensity]}
                            onValueChange={([intensity]) =>
                              updateSettings({
                                animations: { ...settings.animations, intensity }
                              })
                            }
                            max={100}
                            min={0}
                            step={10}
                            className="mt-1"
                          />
                        </div>
                      )}
                    </TabsContent>

                    <TabsContent value="performance" className="space-y-3">
                      <div>
                        <Label className="text-xs">Quality</Label>
                        <Select
                          value={settings.performance.quality}
                          onValueChange={(quality: 'low' | 'medium' | 'high' | 'ultra') =>
                            updateSettings({
                              performance: { ...settings.performance, quality }
                            })
                          }
                        >
                          <SelectTrigger className="h-8 text-xs">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="low">Low</SelectItem>
                            <SelectItem value="medium">Medium</SelectItem>
                            <SelectItem value="high">High</SelectItem>
                            <SelectItem value="ultra">Ultra</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="flex items-center justify-between">
                        <Label className="text-xs">GPU Acceleration</Label>
                        <Switch
                          checked={settings.performance.gpuAcceleration}
                          onCheckedChange={(gpuAcceleration) =>
                            updateSettings({
                              performance: { ...settings.performance, gpuAcceleration }
                            })
                          }
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <Label className="text-xs">Show Monitor</Label>
                        <Switch
                          checked={showPerformanceMonitor}
                          onCheckedChange={setShowPerformanceMonitor}
                        />
                      </div>
                    </TabsContent>
                  </Tabs>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </>
  );
};