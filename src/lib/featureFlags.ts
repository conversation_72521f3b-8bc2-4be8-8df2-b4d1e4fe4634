// Feature flags configuration
export interface FeatureFlags {
  collaboration: {
    enabled: boolean;
    useLocalBackend: boolean;
    localWsUrl: string;
    localShareUrl: string;
    productionWsUrl: string;
    productionShareUrl: string;
  };
  voiceControl: {
    enabled: boolean;
    showUnsupportedWarning: boolean;
  };
  codeFlow: {
    enabled: boolean;
  };
  performanceProfiler: {
    enabled: boolean;
  };
  smartTemplates: {
    enabled: boolean;
  };
  pluginSystem: {
    enabled: boolean;
  };
  agentSystem: {
    enabled: boolean;
  };
  mcpMarketplace: {
    enabled: boolean;
  };
  smartFeatureSelection: {
    enabled: boolean;
  };
}

// Default feature flags - can be overridden by environment variables or user settings
export const defaultFeatureFlags: FeatureFlags = {
  collaboration: {
    enabled: true,
    useLocalBackend: true, // Use local backend by default for development
    localWsUrl: 'ws://localhost:8080/ws',
    localShareUrl: 'http://localhost:3000/join',
    productionWsUrl: 'wss://claudia.app/ws',
    productionShareUrl: 'https://claudia.app/join',
  },
  voiceControl: {
    enabled: true,
    showUnsupportedWarning: true,
  },
  codeFlow: {
    enabled: true,
  },
  performanceProfiler: {
    enabled: true,
  },
  smartTemplates: {
    enabled: true,
  },
  pluginSystem: {
    enabled: true,
  },
  agentSystem: {
    enabled: true,
  },
  mcpMarketplace: {
    enabled: true,
  },
  smartFeatureSelection: {
    enabled: true,
  },
};

// Get feature flags from localStorage or use defaults
export function getFeatureFlags(): FeatureFlags {
  if (typeof window === 'undefined') {
    return defaultFeatureFlags;
  }

  const stored = localStorage.getItem('claudia-feature-flags');
  if (stored) {
    try {
      if (!stored || !stored.trim()) return defaultFeatureFlags;
      const parsed = JSON.parse(stored);
      // Merge with defaults to ensure all flags are present
      return deepMerge(defaultFeatureFlags, parsed);
    } catch (error) {
      // Failed to parse stored feature flags, reset to defaults
      localStorage.removeItem('claudia-feature-flags');
    }
  }

  return defaultFeatureFlags;
}

// Update feature flags in localStorage
export function updateFeatureFlags(updates: Partial<FeatureFlags>): void {
  const current = getFeatureFlags();
  const updated = deepMerge(current, updates);
  try {
    localStorage.setItem('claudia-feature-flags', JSON.stringify(updated));
  } catch (error) {
    console.error('Failed to save feature flags:', error);
  }
  
  // Dispatch custom event to notify components
  window.dispatchEvent(new CustomEvent('feature-flags-updated', { detail: updated }));
}

// Check if a specific feature is enabled
export function isFeatureEnabled(feature: keyof FeatureFlags): boolean {
  const flags = getFeatureFlags();
  return flags[feature]?.enabled ?? false;
}

// Deep merge helper function
function deepMerge<T extends Record<string, any>>(target: T, source: Partial<T>): T {
  const result = { ...target };
  
  for (const key in source) {
    if (source[key] !== undefined) {
      if (typeof source[key] === 'object' && !Array.isArray(source[key]) && source[key] !== null) {
        result[key] = deepMerge(
          result[key] as Record<string, any>,
          source[key] as Record<string, any>
        ) as T[typeof key];
      } else {
        result[key] = source[key] as T[typeof key];
      }
    }
  }
  
  return result;
}

// React hook for using feature flags
export function useFeatureFlags() {
  const [flags, setFlags] = useState(getFeatureFlags());

  useEffect(() => {
    const handleUpdate = (event: CustomEvent) => {
      setFlags(event.detail);
    };

    window.addEventListener('feature-flags-updated', handleUpdate as EventListener);
    return () => {
      window.removeEventListener('feature-flags-updated', handleUpdate as EventListener);
    };
  }, []);

  return {
    flags,
    updateFlags: updateFeatureFlags,
    isEnabled: (feature: keyof FeatureFlags) => flags[feature]?.enabled ?? false,
  };
}

// Import for React hooks
import { useState, useEffect } from 'react';